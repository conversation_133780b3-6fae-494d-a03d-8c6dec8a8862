# OutlookRegister 更新说明 - 迁移到 undetected-chromedriver

## 🔄 更新概述

本次更新将 OutlookRegister 程序从 Playwright 迁移到了 undetected-chromedriver，以提供更好的反检测能力和更高的成功率。

## 📋 主要变更

### 1. 依赖包变更
- **移除**: `playwright`
- **新增**: `undetected-chromedriver`, `selenium`
- **保留**: `faker`, `requests`

### 2. 核心文件修改
- `OutlookRegister.py` - 完全重写浏览器操作逻辑
- `get_token.py` - 适配 Selenium WebDriver API
- `requirements.txt` - 更新依赖包列表
- `启动器.py` - 更新依赖检查逻辑
- `启动脚本.bat` - 更新依赖安装命令

### 3. 新增文件
- `test_undetected.py` - undetected-chromedriver 测试工具
- `OutlookRegister_playwright_backup.py` - Playwright 版本备份

## 🚀 使用方法

### 安装依赖
```bash
pip install undetected-chromedriver selenium faker requests
```

### 测试安装
```bash
python test_undetected.py
```

### 运行程序
```bash
python OutlookRegister.py
```
或使用启动器：
```bash
python 启动器.py
```

## ✨ 新特性

### 1. 更强的反检测能力
- 使用 undetected-chromedriver 绕过常见的机器人检测
- 自动处理 Chrome 的反自动化检测
- 更好的指纹伪装

### 2. 更稳定的浏览器操作
- 基于 Selenium WebDriver 的稳定 API
- 更好的元素等待和异常处理
- 支持更多的浏览器配置选项

### 3. 保持原有功能
- 所有原有的注册功能保持不变
- 代理支持
- OAuth2 token 获取
- 并发注册

## 🔧 配置说明

配置文件 `config.json` 保持不变，支持以下选项：

```json
{
    "browser_path": "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
    "proxy": "socks5://127.0.0.1:10808",
    "Bot_protection_wait": 60,
    "max_captcha_retries": 5,
    "concurrent_flows": 2,
    "max_tasks": 3,
    "enable_oauth2": false,
    "client_id": "",
    "redirect_url": "",
    "Scopes": [
        "offline_access",
        "https://graph.microsoft.com/Mail.ReadWrite",
        "https://graph.microsoft.com/Mail.Send",
        "https://graph.microsoft.com/User.Read"
    ]
}
```

## 🐛 故障排除

### 1. Chrome 浏览器问题
如果遇到浏览器启动失败：
- 确保安装了最新版本的 Chrome 浏览器
- 检查 `browser_path` 配置是否正确
- 尝试使用默认 Chrome（将 `browser_path` 设为空）

### 2. 代理问题
如果代理连接失败：
- 检查代理服务器是否正常运行
- 确认代理地址格式正确（如：`socks5://127.0.0.1:10808`）
- 尝试不使用代理进行测试

### 3. 依赖包问题
如果遇到导入错误：
```bash
pip uninstall playwright
pip install undetected-chromedriver selenium
```

## 📊 性能对比

| 特性 | Playwright | undetected-chromedriver |
|------|------------|-------------------------|
| 反检测能力 | 中等 | 强 |
| 启动速度 | 快 | 中等 |
| 内存占用 | 低 | 中等 |
| 稳定性 | 高 | 高 |
| 维护成本 | 中等 | 低 |

## 🔄 回滚方法

如果需要回滚到 Playwright 版本：

1. 恢复备份文件：
```bash
copy OutlookRegister_playwright_backup.py OutlookRegister.py
```

2. 安装 Playwright：
```bash
pip uninstall undetected-chromedriver selenium
pip install playwright
python -m playwright install chromium
```

3. 恢复原始的 `requirements.txt`、`启动器.py` 和 `启动脚本.bat`

## 📝 注意事项

1. **Chrome 版本兼容性**: undetected-chromedriver 会自动下载匹配的 ChromeDriver，但建议使用最新版本的 Chrome
2. **代理配置**: 代理配置方式略有不同，但配置文件格式保持兼容
3. **性能**: 首次启动可能较慢，因为需要下载 ChromeDriver
4. **反检测**: 虽然反检测能力更强，但仍建议配合优质代理使用

## 🎯 建议

1. **测试环境**: 建议先在测试环境中验证新版本的稳定性
2. **代理质量**: 使用高质量的代理服务以获得最佳效果
3. **并发控制**: 建议将并发数控制在 1-3 之间
4. **监控**: 密切监控注册成功率，如有问题及时调整配置

---

**更新日期**: 2025-07-28  
**版本**: v2.0 (undetected-chromedriver)  
**兼容性**: Windows 10/11, Python 3.7+
