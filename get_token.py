import json
import base64
import random
import string
import winreg
import hashlib
import secrets
import requests
from datetime import datetime
from urllib.parse import quote, parse_qs

def get_proxy():
    try:
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\CurrentVersion\Internet Settings") as key:
            proxy_enable, _ = winreg.QueryValueEx(key, "ProxyEnable")
            proxy_server, _ = winreg.QueryValueEx(key, "ProxyServer")
            if proxy_enable and proxy_server:
                return {"http": f"http://{proxy_server}", "https": f"http://{proxy_server}"}
    except WindowsError:
        pass
    return {"http": None, "https": None}

def generate_code_verifier(length=128):
    alphabet = string.ascii_letters + string.digits + '-._~'
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def generate_code_challenge(code_verifier):
    sha256_hash = hashlib.sha256(code_verifier.encode()).digest()
    return base64.urlsafe_b64encode(sha256_hash).decode().rstrip('=')

def handle_oauth2_form(driver, email):
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    import time

    try:
        wait = WebDriverWait(driver, 20)

        # 填写邮箱地址
        email_input = wait.until(EC.presence_of_element_located((By.NAME, 'loginfmt')))
        email_input.clear()
        email_input.send_keys(f'{email}@outlook.com')

        # 点击下一步
        next_button = wait.until(EC.element_to_be_clickable((By.ID, 'idSIButton9')))
        next_button.click()

        # 处理跳过按钮（如果存在）
        try:
            secondary_button = WebDriverWait(driver, 3).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-testid="secondaryButton"]'))
            )
            secondary_button.click()

            # 多次点击跳过按钮
            for i in range(3):
                try:
                    time.sleep(random.randint(1600, 2000) / 1000.0)
                    secondary_button = WebDriverWait(driver, 6).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-testid="secondaryButton"]'))
                    )
                    secondary_button.click()
                except:
                    break
        except:
            pass

    except Exception as e:
        print(f"OAuth2表单处理异常: {e}")
        pass

    # 处理应用授权按钮
    try:
        consent_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-testid="appConsentPrimaryButton"]'))
        )
        consent_button.click()
    except:
        pass

def get_access_token(driver, email):
    from selenium.webdriver.support.ui import WebDriverWait
    import time

    with open('config.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    SCOPES = data['Scopes']
    client_id = data['client_id']
    redirect_url = data['redirect_url']

    code_verifier = generate_code_verifier()
    code_challenge = generate_code_challenge(code_verifier)
    scope = ' '.join(SCOPES)
    params = {
        'client_id': client_id,
        'response_type': 'code',
        'redirect_uri': redirect_url,
        'scope': scope,
        'response_mode': 'query',
        'prompt': 'select_account',
        'code_challenge': code_challenge,
        'code_challenge_method': 'S256'
    }

    max_time = 2
    current_times = 0
    while current_times < max_time:
        try:
            time.sleep(0.25)
            url = f"https://login.microsoftonline.com/common/oauth2/v2.0/authorize?{'&'.join(f'{k}={quote(v)}' for k,v in params.items())}"
            driver.get(url)
            break
        except:
            current_times = current_times + 1
            if current_times == max_time:
                return False, False, False
            continue

    # 等待重定向到回调URL
    try:
        wait = WebDriverWait(driver, 50)

        # 处理OAuth2表单
        handle_oauth2_form(driver, email)

        # 等待重定向到回调URL
        wait.until(lambda d: redirect_url in d.current_url)
        callback_url = driver.current_url

        if 'code=' not in callback_url:
            print("Authorization failed: No code in callback URL")
            return False, False, False

        auth_code = parse_qs(callback_url.split('?')[1])['code'][0]

    except Exception as e:
        print(f"OAuth2授权失败: {e}")
        return False, False, False

    token_data = {
        'client_id': client_id,
        'code': auth_code,
        'redirect_uri': redirect_url,
        'grant_type': 'authorization_code',
        'code_verifier': code_verifier,
        'scope': ' '.join(SCOPES)
    }

    response = requests.post('https://login.microsoftonline.com/common/oauth2/v2.0/token', data=token_data, headers={
        'Content-Type': 'application/x-www-form-urlencoded'
    }, proxies=get_proxy())

    if 'refresh_token' in response.json():

        tokens = response.json()
        token_data = {
            'refresh_token': tokens['refresh_token'],
            'access_token': tokens.get('access_token', ''),
            'expires_at': datetime.now().timestamp() + tokens['expires_in']
    }
        refresh_token = token_data['refresh_token']
        access_token = token_data['access_token']
        expire_at = token_data['expires_at']
        return refresh_token, access_token, expire_at

    else:

        return False, False, False