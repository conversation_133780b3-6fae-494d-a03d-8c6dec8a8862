@echo off
title Outlook Registration Tool

echo ========================================
echo      Outlook Registration Tool
echo ========================================
echo.

:: Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not found!
    echo Please install Python from https://www.python.org/
    pause
    exit /b 1
)

echo [OK] Python found
echo.

:: Check files
if not exist "OutlookRegister.py" (
    echo [ERROR] OutlookRegister.py not found!
    pause
    exit /b 1
)

if not exist "config.json" (
    echo [ERROR] config.json not found!
    pause
    exit /b 1
)

echo [OK] Required files found
echo.

:: Check dependencies
echo Checking Python packages...
python -c "import undetected_chromedriver, selenium, faker" >nul 2>&1
if errorlevel 1 (
    echo [INFO] Installing required packages...
    pip install undetected-chromedriver selenium faker requests
    if errorlevel 1 (
        echo [ERROR] Failed to install packages
        echo Please run manually: pip install undetected-chromedriver selenium faker requests
        pause
        exit /b 1
    )
)

echo [OK] Dependencies ready
echo.

:: Show config
echo Current configuration:
python -c "import json; f=open('config.json','r',encoding='utf-8'); data=json.load(f); print('  Proxy:', data.get('proxy', 'None')); print('  Concurrent flows:', data.get('concurrent_flows', 1)); print('  Max tasks:', data.get('max_tasks', 5)); f.close()"
echo.

:: Confirm start
set /p choice="Start registration tool? (Y/N): "
if /i "%choice%" neq "Y" (
    echo Cancelled
    pause
    exit /b 0
)

echo.
echo ========================================
echo        Starting Registration Tool
echo ========================================
echo.

:: Run main program
python OutlookRegister.py

echo.
echo ========================================
echo           Program Finished
echo ========================================
pause
