#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
V2Ray代理连接测试脚本
"""

import requests
import json
from playwright.sync_api import sync_playwright

def test_requests_proxy(proxy_url):
    """使用requests测试代理连接"""
    print(f"\n=== 测试代理: {proxy_url} ===")
    
    proxies = {
        'http': proxy_url,
        'https': proxy_url
    }
    
    try:
        # 测试获取IP地址
        print("正在测试代理连接...")
        response = requests.get('http://httpbin.org/ip', proxies=proxies, timeout=10)
        if response.status_code == 200:
            ip_info = response.json()
            print(f"✓ 代理连接成功")
            print(f"  当前IP: {ip_info['origin']}")
            return True
        else:
            print(f"✗ 代理连接失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 代理连接异常: {e}")
        return False

def test_playwright_proxy(proxy_url, browser_path=""):
    """使用Playwright测试代理连接"""
    print(f"\n=== 使用Playwright测试代理: {proxy_url} ===")
    
    try:
        p = sync_playwright().start()
        launch_options = {
            "headless": True,  # 无头模式测试
            "proxy": {
                "server": proxy_url,
                "bypass": "localhost",
            },
        }
        
        if browser_path and browser_path.strip():
            launch_options["executable_path"] = browser_path
        
        browser = p.chromium.launch(**launch_options)
        page = browser.new_page()
        
        # 测试访问IP检查网站
        print("正在访问IP检查网站...")
        page.goto("http://httpbin.org/ip", timeout=15000)
        content = page.content()
        
        if "origin" in content:
            print("✓ Playwright代理连接成功")
            # 尝试访问Outlook
            print("正在测试访问Outlook...")
            page.goto("https://outlook.live.com", timeout=15000)
            title = page.title()
            print(f"✓ Outlook访问成功，页面标题: {title}")
            browser.close()
            p.stop()
            return True
        else:
            print("✗ Playwright代理连接失败")
            browser.close()
            p.stop()
            return False
            
    except Exception as e:
        print(f"✗ Playwright代理测试异常: {e}")
        try:
            browser.close()
            p.stop()
        except:
            pass
        return False

def main():
    print("=== V2Ray代理连接测试工具 ===")
    
    # 读取配置文件
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        current_proxy = config.get('proxy', '')
        browser_path = config.get('browser_path', '')
        print(f"当前配置的代理: {current_proxy}")
    except Exception as e:
        print(f"读取配置文件失败: {e}")
        current_proxy = ""
        browser_path = ""
    
    # 常见的V2Ray代理端口配置
    common_proxies = [
        "http://127.0.0.1:10809",  # V2RayN默认HTTP端口
        "http://127.0.0.1:8080",   # 常用HTTP端口
        "http://127.0.0.1:1087",   # 另一个常用端口
        "http://127.0.0.1:7897",   # 当前配置的端口
        "socks5://127.0.0.1:10808", # V2RayN默认SOCKS端口
        "socks5://127.0.0.1:1080",  # 常用SOCKS端口
    ]
    
    print("\n开始测试常见的V2Ray代理端口...")
    working_proxies = []
    
    for proxy in common_proxies:
        if test_requests_proxy(proxy):
            working_proxies.append(proxy)
    
    if working_proxies:
        print(f"\n找到可用的代理配置:")
        for i, proxy in enumerate(working_proxies, 1):
            print(f"{i}. {proxy}")
        
        # 测试第一个可用代理的Playwright兼容性
        best_proxy = working_proxies[0]
        print(f"\n使用最佳代理 {best_proxy} 进行Playwright测试...")
        if test_playwright_proxy(best_proxy, browser_path):
            print(f"\n✓ 推荐使用代理: {best_proxy}")
            print(f"请将config.json中的proxy字段修改为: \"{best_proxy}\"")
        else:
            print(f"\n✗ 代理 {best_proxy} 在Playwright中不可用")
    else:
        print("\n✗ 未找到可用的代理配置")
        print("\n请检查:")
        print("1. V2Ray客户端是否正在运行")
        print("2. 本地代理端口设置是否正确")
        print("3. 防火墙是否阻止了本地连接")
    
    print("\n测试完成")
    input("按回车键退出...")

if __name__ == "__main__":
    main()
