# Outlook注册工具使用说明

## 🚀 快速开始

### 方法1：使用Python启动器（推荐）
```bash
python 启动器.py
```

### 方法2：使用批处理文件
```bash
启动脚本.bat
```

### 方法3：直接运行（如果遇到问题）
```bash
python OutlookRegister.py
```

## 📋 关于代理的说明

### ❌ 不使用代理
- **优点**：配置简单，无需额外设置
- **缺点**：
  - 成功率较低（约10-30%）
  - IP容易被限制
  - 一次只能注册1-3个账户
  - 需要等待几小时才能再次尝试

### ✅ 使用代理（强烈推荐）
- **优点**：
  - 成功率较高（约60-80%）
  - 可以注册更多账户
  - 避免真实IP被限制
- **缺点**：需要配置代理服务器

## 🛠️ 配置管理

### 使用配置管理器
```bash
python 配置管理器.py
```

配置管理器可以帮您：
- 切换到无代理配置
- 设置自定义代理
- 修改注册参数
- 查看当前配置

### 手动配置文件

#### 无代理配置示例：
```json
{
    "browser_path": "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
    "proxy": "",
    "Bot_protection_wait": 60,
    "max_captcha_retries": 5,
    "concurrent_flows": 1,
    "max_tasks": 3,
    "enable_oauth2": false
}
```

#### 有代理配置示例：
```json
{
    "browser_path": "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
    "proxy": "socks5://127.0.0.1:10808",
    "Bot_protection_wait": 30,
    "max_captcha_retries": 5,
    "concurrent_flows": 3,
    "max_tasks": 10,
    "enable_oauth2": false
}
```

## 📊 参数说明

| 参数 | 说明 | 无代理推荐值 | 有代理推荐值 |
|------|------|-------------|-------------|
| `concurrent_flows` | 并发数 | 1 | 3-5 |
| `max_tasks` | 最大任务数 | 3 | 10-50 |
| `Bot_protection_wait` | 机器人保护等待时间(秒) | 60 | 30 |
| `max_captcha_retries` | 验证码重试次数 | 5 | 5 |

## 🔧 常见问题解决

### 1. 脚本一闪而过
- **原因**：程序遇到错误或正常结束
- **解决**：使用提供的启动脚本，会显示详细信息并等待用户确认

### 2. IP被限制错误
```
[Error: IP or broswer] - 当前IP注册频率过快
```
- **解决方案**：
  - 等待几小时后再试
  - 更换代理IP
  - 降低并发数和任务数

### 3. 浏览器启动失败
- **检查**：Chrome浏览器路径是否正确
- **解决**：修改`config.json`中的`browser_path`

### 4. 代理连接失败
- **测试代理**：运行 `python test_proxy.py`
- **检查**：代理服务器是否正在运行
- **尝试**：使用不同的代理端口

## 📁 输出文件

注册成功的账户信息会保存在 `Results` 文件夹中：
- `logged_email.txt` - 成功注册的邮箱
- `unlogged_email.txt` - 注册失败的邮箱
- `outlook_token.txt` - OAuth2令牌（如果启用）

## ⚠️ 注意事项

1. **首次使用建议**：
   - 先使用无代理模式测试1-2个账户
   - 确认程序正常工作后再考虑使用代理

2. **成功率优化**：
   - 使用高质量的代理IP
   - 降低并发数
   - 增加等待时间
   - 使用指纹浏览器

3. **法律合规**：
   - 仅用于合法用途
   - 遵守Microsoft服务条款
   - 不要进行大规模批量注册

## 🆘 获取帮助

如果遇到问题：
1. 查看控制台输出的错误信息
2. 检查配置文件是否正确
3. 尝试使用配置管理器重新配置
4. 运行代理测试脚本检查网络连接
