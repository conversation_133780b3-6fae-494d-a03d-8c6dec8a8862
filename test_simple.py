import json
from playwright.sync_api import sync_playwright

def test_browser():
    """简单测试浏览器启动"""
    try:
        # 读取配置
        with open('config.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        browser_path = data['browser_path']
        proxy = data['proxy']
        
        print(f"Browser path: {browser_path}")
        print(f"Proxy: {proxy}")
        
        # 启动Playwright
        p = sync_playwright().start()
        print("Playwright started successfully")
        
        # 配置启动选项
        launch_options = {
            "headless": False,
        }
        
        # 如果有代理设置，添加代理
        if proxy:
            launch_options["proxy"] = {
                "server": proxy,
                "bypass": "localhost",
            }
        
        # 只有当browser_path不为空时才设置executable_path
        if browser_path and browser_path.strip():
            launch_options["executable_path"] = browser_path
            print(f"Using custom browser: {browser_path}")
        else:
            print("Using default Playwright Chromium")
        
        # 启动浏览器
        browser = p.chromium.launch(**launch_options)
        print("Browser launched successfully")
        
        # 创建页面
        page = browser.new_page()
        print("New page created")
        
        # 访问测试页面
        page.goto("https://www.google.com")
        print("Navigated to Google")
        
        # 等待几秒钟
        page.wait_for_timeout(3000)
        
        # 关闭浏览器
        browser.close()
        p.stop()
        print("Browser closed successfully")
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    print("Testing browser startup...")
    success = test_browser()
    if success:
        print("✅ Test passed!")
    else:
        print("❌ Test failed!")
    
    input("Press Enter to exit...")
