#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
反检测能力专项测试工具
"""

import json
import time
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def test_anti_detection():
    """测试反检测能力"""
    driver = None
    try:
        print("=" * 60)
        print("         反检测能力专项测试")
        print("=" * 60)
        print()
        
        # 读取配置
        with open('config.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        proxy = data.get('proxy', '')
        browser_path = data.get('browser_path', '')
        
        print(f"代理设置: {proxy}")
        print(f"浏览器路径: {browser_path}")
        print()
        
        # 配置Chrome选项 - 使用兼容的反检测配置
        options = uc.ChromeOptions()

        # 添加基本反检测选项
        options.add_argument('--no-first-run')
        options.add_argument('--no-default-browser-check')
        options.add_argument('--disable-blink-features=AutomationControlled')

        # 设置用户代理
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # 添加代理配置
        if proxy and proxy.strip():
            options.add_argument(f'--proxy-server={proxy}')
            print(f"✓ 使用代理: {proxy}")
        else:
            print("✓ 使用直连")
        
        # 设置浏览器路径
        if browser_path and browser_path.strip():
            import os
            if os.path.exists(browser_path):
                options.binary_location = browser_path
                print(f"✓ 使用自定义浏览器: {browser_path}")
            else:
                print(f"⚠ 指定的浏览器路径不存在，使用默认Chrome")
        else:
            print("✓ 使用默认Chrome")
        
        print("\n正在启动 undetected-chromedriver...")
        
        # 创建driver实例
        driver = uc.Chrome(options=options, version_main=None)
        print("✅ 浏览器启动成功")
        
        # 设置窗口大小
        driver.set_window_size(1280, 720)
        
        # 执行反检测脚本
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # 测试网站列表
        test_sites = [
            {
                'name': 'Bot Detection Test',
                'url': 'https://bot.sannysoft.com/',
                'description': '综合机器人检测测试'
            },
            {
                'name': 'WebDriver Detection',
                'url': 'https://intoli.com/blog/not-possible-to-block-chrome-headless/chrome-headless-test.html',
                'description': 'WebDriver检测测试'
            },
            {
                'name': 'Fingerprint Test',
                'url': 'https://fingerprintjs.com/demo/',
                'description': '浏览器指纹测试'
            }
        ]
        
        results = []
        
        for i, site in enumerate(test_sites, 1):
            print(f"\n{i}. 测试 {site['name']}...")
            print(f"   描述: {site['description']}")
            print(f"   URL: {site['url']}")
            
            try:
                driver.get(site['url'])
                time.sleep(5)  # 等待页面加载
                
                title = driver.title
                print(f"   页面标题: {title}")
                
                # 保存截图
                screenshot_name = f"detection_test_{i}_{site['name'].replace(' ', '_').lower()}.png"
                driver.save_screenshot(screenshot_name)
                print(f"   ✅ 截图已保存: {screenshot_name}")
                
                results.append({
                    'site': site['name'],
                    'status': '成功',
                    'title': title,
                    'screenshot': screenshot_name
                })
                
            except Exception as e:
                print(f"   ❌ 访问失败: {e}")
                results.append({
                    'site': site['name'],
                    'status': '失败',
                    'error': str(e)
                })
        
        # 特殊测试：检查navigator.webdriver属性
        print(f"\n4. 检查 navigator.webdriver 属性...")
        try:
            webdriver_value = driver.execute_script("return navigator.webdriver")
            print(f"   navigator.webdriver = {webdriver_value}")
            if webdriver_value is None or webdriver_value == False:
                print("   ✅ webdriver属性已隐藏")
            else:
                print("   ⚠ webdriver属性未隐藏")
        except Exception as e:
            print(f"   ❌ 检查失败: {e}")
        
        # 检查用户代理
        print(f"\n5. 检查用户代理...")
        try:
            user_agent = driver.execute_script("return navigator.userAgent")
            print(f"   User-Agent: {user_agent}")
            if "HeadlessChrome" in user_agent:
                print("   ⚠ 检测到HeadlessChrome标识")
            else:
                print("   ✅ 用户代理正常")
        except Exception as e:
            print(f"   ❌ 检查失败: {e}")
        
        # 检查Chrome对象
        print(f"\n6. 检查Chrome对象...")
        try:
            chrome_obj = driver.execute_script("return window.chrome")
            if chrome_obj:
                print("   ✅ Chrome对象存在")
            else:
                print("   ⚠ Chrome对象不存在")
        except Exception as e:
            print(f"   ❌ 检查失败: {e}")
        
        # 显示测试结果
        print("\n" + "=" * 60)
        print("测试结果汇总:")
        print("=" * 60)
        
        for result in results:
            status_icon = "✅" if result['status'] == '成功' else "❌"
            print(f"{status_icon} {result['site']}: {result['status']}")
            if 'title' in result:
                print(f"   页面标题: {result['title']}")
            if 'screenshot' in result:
                print(f"   截图文件: {result['screenshot']}")
            if 'error' in result:
                print(f"   错误信息: {result['error']}")
            print()
        
        print("建议:")
        print("1. 查看生成的截图文件，绿色表示通过检测，红色表示被检测")
        print("2. 特别关注 bot.sannysoft.com 的检测结果")
        print("3. 如果大部分检测项为绿色，说明反检测效果良好")
        print("4. 如果检测到问题，可以调整Chrome选项配置")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
        
    finally:
        if driver:
            try:
                print(f"\n正在关闭浏览器...")
                time.sleep(2)  # 给用户一点时间查看结果
                driver.quit()
                print("✅ 浏览器已关闭")
            except:
                pass

if __name__ == "__main__":
    success = test_anti_detection()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 反检测测试完成！")
        print("请查看生成的截图文件来评估反检测效果。")
    else:
        print("❌ 反检测测试失败！")
        print("请检查Chrome浏览器安装和网络连接。")
    print("=" * 60)
    
    input("\n按回车键退出...")
