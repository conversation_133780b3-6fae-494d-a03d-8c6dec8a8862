#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器 - 用于切换不同的配置文件
"""

import os
import json
import shutil

def load_config(config_file):
    """加载配置文件"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载配置文件失败：{e}")
        return None

def save_config(config_data, config_file='config.json'):
    """保存配置文件"""
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=4)
        return True
    except Exception as e:
        print(f"❌ 保存配置文件失败：{e}")
        return False

def show_config(config_data):
    """显示配置信息"""
    print("配置信息：")
    print(f"  浏览器路径: {config_data.get('browser_path', '默认')}")
    print(f"  代理设置: {config_data.get('proxy', '未配置') or '未配置'}")
    print(f"  并发数: {config_data.get('concurrent_flows', 1)}")
    print(f"  最大任务数: {config_data.get('max_tasks', 5)}")
    print(f"  机器人保护等待时间: {config_data.get('Bot_protection_wait', 15)}秒")
    print(f"  启用OAuth2: {config_data.get('enable_oauth2', False)}")
    if config_data.get('info'):
        print(f"  说明: {config_data['info']}")

def main():
    """主函数"""
    print("=" * 60)
    print("                    配置管理器")
    print("=" * 60)
    print()
    
    # 检查当前配置
    current_config = load_config('config.json')
    if current_config:
        print("当前配置：")
        show_config(current_config)
        print()
    
    print("可用的配置选项：")
    print("1. 无代理配置（适合测试，成功率较低）")
    print("2. 自定义代理配置")
    print("3. 修改当前配置")
    print("4. 查看当前配置")
    print("0. 退出")
    print()
    
    while True:
        choice = input("请选择操作 (0-4): ").strip()
        
        if choice == '0':
            print("退出配置管理器")
            break
            
        elif choice == '1':
            # 使用无代理配置
            if os.path.exists('config_no_proxy.json'):
                no_proxy_config = load_config('config_no_proxy.json')
                if no_proxy_config and save_config(no_proxy_config):
                    print("✅ 已切换到无代理配置")
                    print()
                    show_config(no_proxy_config)
                    print()
                    print("⚠️  注意事项：")
                    print("   - 不使用代理可能导致IP被快速限制")
                    print("   - 建议一次只注册1-3个账户")
                    print("   - 如果遇到IP限制，请等待几小时后再试")
                else:
                    print("❌ 切换配置失败")
            else:
                print("❌ 未找到无代理配置文件")
                
        elif choice == '2':
            # 自定义代理配置
            print("\n请输入代理配置：")
            proxy = input("代理地址 (例如: socks5://127.0.0.1:10808): ").strip()
            concurrent = input("并发数 (建议1-3): ").strip()
            max_tasks = input("最大任务数 (建议3-10): ").strip()
            wait_time = input("机器人保护等待时间/秒 (建议30-60): ").strip()
            
            try:
                new_config = current_config.copy() if current_config else {}
                new_config['proxy'] = proxy
                new_config['concurrent_flows'] = int(concurrent) if concurrent else 1
                new_config['max_tasks'] = int(max_tasks) if max_tasks else 5
                new_config['Bot_protection_wait'] = int(wait_time) if wait_time else 30
                
                if save_config(new_config):
                    print("✅ 配置保存成功")
                    show_config(new_config)
                else:
                    print("❌ 配置保存失败")
            except ValueError:
                print("❌ 输入的数值格式错误")
                
        elif choice == '3':
            # 修改当前配置
            if not current_config:
                print("❌ 当前没有有效的配置文件")
                continue
                
            print("\n当前配置：")
            show_config(current_config)
            print("\n请选择要修改的项目：")
            print("1. 代理设置")
            print("2. 并发数")
            print("3. 最大任务数")
            print("4. 机器人保护等待时间")
            
            modify_choice = input("请选择 (1-4): ").strip()
            
            if modify_choice == '1':
                new_proxy = input(f"新的代理地址 (当前: {current_config.get('proxy', '未配置')}): ").strip()
                current_config['proxy'] = new_proxy
            elif modify_choice == '2':
                new_concurrent = input(f"新的并发数 (当前: {current_config.get('concurrent_flows', 1)}): ").strip()
                if new_concurrent:
                    current_config['concurrent_flows'] = int(new_concurrent)
            elif modify_choice == '3':
                new_max_tasks = input(f"新的最大任务数 (当前: {current_config.get('max_tasks', 5)}): ").strip()
                if new_max_tasks:
                    current_config['max_tasks'] = int(new_max_tasks)
            elif modify_choice == '4':
                new_wait_time = input(f"新的等待时间/秒 (当前: {current_config.get('Bot_protection_wait', 15)}): ").strip()
                if new_wait_time:
                    current_config['Bot_protection_wait'] = int(new_wait_time)
            else:
                print("❌ 无效选择")
                continue
                
            if save_config(current_config):
                print("✅ 配置修改成功")
                show_config(current_config)
            else:
                print("❌ 配置修改失败")
                
        elif choice == '4':
            # 查看当前配置
            if current_config:
                print("\n当前配置：")
                show_config(current_config)
            else:
                print("❌ 当前没有有效的配置文件")
                
        else:
            print("❌ 无效选择，请重新输入")
        
        print()

if __name__ == "__main__":
    main()
    input("按回车键退出...")
