#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
undetected-chromedriver 测试脚本
"""

import json
import time
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def test_undetected_chrome():
    """测试 undetected-chromedriver 启动和基本功能"""
    driver = None
    try:
        # 读取配置
        with open('config.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        browser_path = data.get('browser_path', '')
        proxy = data.get('proxy', '')
        
        print(f"浏览器路径: {browser_path}")
        print(f"代理设置: {proxy}")
        
        # 配置Chrome选项
        options = uc.ChromeOptions()

        # 添加基本选项
        options.add_argument('--no-first-run')
        options.add_argument('--no-default-browser-check')
        
        # 添加代理配置（如果提供了代理）
        if proxy and proxy.strip():
            options.add_argument(f'--proxy-server={proxy}')
            print(f"使用代理: {proxy}")
        else:
            print("未配置代理，使用直连")

        # 如果指定了浏览器路径，使用自定义浏览器
        if browser_path and browser_path.strip():
            import os
            if os.path.exists(browser_path):
                options.binary_location = browser_path
                print(f"使用自定义浏览器: {browser_path}")
            else:
                print(f"警告：指定的浏览器路径不存在: {browser_path}")
                print("将使用默认Chrome浏览器")
        else:
            print("使用默认Chrome浏览器")

        print("正在启动 undetected-chromedriver...")
        
        # 创建undetected-chromedriver实例
        driver = uc.Chrome(options=options, version_main=None)
        print("✅ undetected-chromedriver 启动成功")
        
        # 设置窗口大小
        driver.set_window_size(1280, 720)
        
        # 执行反检测脚本
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # 测试访问网站
        print("正在访问测试网站...")
        driver.get("https://bot.sannysoft.com/")
        
        # 等待页面加载
        time.sleep(5)
        
        # 检查页面标题
        title = driver.title
        print(f"页面标题: {title}")
        
        # 测试访问Google
        print("正在测试访问Google...")
        driver.get("https://www.google.com")
        time.sleep(3)
        
        google_title = driver.title
        print(f"Google页面标题: {google_title}")
        
        # 测试访问Outlook
        print("正在测试访问Outlook...")
        driver.get("https://outlook.live.com")
        time.sleep(5)
        
        outlook_title = driver.title
        print(f"Outlook页面标题: {outlook_title}")
        
        print("✅ 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
        
    finally:
        if driver:
            try:
                print("正在关闭浏览器...")
                driver.quit()
                print("✅ 浏览器已关闭")
            except:
                pass

def test_detection():
    """测试反检测能力"""
    driver = None
    try:
        print("\n=== 反检测能力测试 ===")
        
        options = uc.ChromeOptions()
        options.add_argument('--no-first-run')
        options.add_argument('--no-service-autorun')
        options.add_argument('--no-default-browser-check')
        
        driver = uc.Chrome(options=options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # 访问检测网站
        print("正在访问机器人检测网站...")
        driver.get("https://bot.sannysoft.com/")
        
        # 等待页面完全加载
        time.sleep(10)
        
        # 截图保存结果
        try:
            driver.save_screenshot("detection_test.png")
            print("✅ 检测结果已保存为 detection_test.png")
        except:
            pass
            
        print("请查看浏览器窗口中的检测结果")
        print("绿色表示通过检测，红色表示被检测到")
        
        # 保持窗口打开一段时间供用户查看
        input("按回车键继续...")
        
        return True
        
    except Exception as e:
        print(f"❌ 反检测测试失败: {e}")
        return False
        
    finally:
        if driver:
            try:
                driver.quit()
            except:
                pass

if __name__ == "__main__":
    print("=" * 60)
    print("         undetected-chromedriver 测试工具")
    print("=" * 60)
    print()
    
    # 基本功能测试
    print("1. 基本功能测试")
    success1 = test_undetected_chrome()
    
    if success1:
        print("\n" + "=" * 40)
        choice = input("是否进行反检测能力测试？(y/n): ").strip().lower()
        if choice == 'y':
            success2 = test_detection()
        else:
            success2 = True
    else:
        success2 = False
    
    print("\n" + "=" * 60)
    print("测试结果:")
    print(f"  基本功能测试: {'✅ 通过' if success1 else '❌ 失败'}")
    if success1:
        print(f"  反检测测试: {'✅ 通过' if success2 else '❌ 失败'}")
    print("=" * 60)
    
    if success1:
        print("\n✅ undetected-chromedriver 可以正常使用！")
        print("现在可以运行主程序进行Outlook注册了。")
    else:
        print("\n❌ undetected-chromedriver 测试失败")
        print("请检查Chrome浏览器是否正确安装，或尝试安装最新版本的Chrome。")
    
    input("\n按回车键退出...")
