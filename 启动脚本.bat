@echo off
chcp 936 >nul
title Outlook Registration Tool Launcher

echo ========================================
echo           Outlook Registration Tool
echo ========================================
echo.

:: Check Python installation
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not found, please install Python first
    echo.
    echo Please visit https://www.python.org/downloads/ to download Python
    pause
    exit /b 1
)

echo [OK] Python environment check passed
echo.

:: 检查必要文件
if not exist "OutlookRegister.py" (
    echo ❌ 错误：未找到 OutlookRegister.py 文件
    pause
    exit /b 1
)

if not exist "config.json" (
    echo ❌ 错误：未找到 config.json 配置文件
    pause
    exit /b 1
)

echo ✅ 必要文件检查通过
echo.

:: 检查依赖包
echo 正在检查Python依赖包...
python -c "import undetected_chromedriver, selenium, faker" >nul 2>&1
if errorlevel 1 (
    echo ❌ 缺少必要的Python包，正在安装...
    echo.
    pip install undetected-chromedriver selenium faker requests
    if errorlevel 1 (
        echo ❌ 依赖包安装失败，请手动运行：
        echo pip install undetected-chromedriver selenium faker requests
        pause
        exit /b 1
    )
)

echo ✅ 依赖包检查通过
echo.

:: 显示当前配置
echo 当前配置信息：
python -c "import json; f=open('config.json','r',encoding='utf-8'); data=json.load(f); print('  代理设置:', data.get('proxy', '未配置')); print('  并发数:', data.get('concurrent_flows', 1)); print('  最大任务数:', data.get('max_tasks', 5)); print('  浏览器路径:', data.get('browser_path', '默认')); f.close()"
echo.

:: 询问是否继续
set /p choice="是否开始运行？(Y/N): "
if /i "%choice%" neq "Y" (
    echo 已取消运行
    pause
    exit /b 0
)

echo.
echo ========================================
echo           开始运行注册工具
echo ========================================
echo.

:: 运行主程序
python OutlookRegister.py

echo.
echo ========================================
echo           程序运行结束
echo ========================================
pause
