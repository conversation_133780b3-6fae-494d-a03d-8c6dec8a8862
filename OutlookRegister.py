import os
import time
import json
import random
import string
import secrets
from faker import Faker
from get_token import get_access_token
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from concurrent.futures import ThreadPoolExecutor

def generate_strong_password(length=16):

    chars = string.ascii_letters + string.digits + "!@#$%^&*"

    while True:
        password = ''.join(secrets.choice(chars) for _ in range(length))

        if (any(c.islower() for c in password) 
                and any(c.isupper() for c in password)
                and any(c.isdigit() for c in password)
                and any(c in "!@#$%^&*" for c in password)):
            return password


def random_email(length):

    first_char = random.choice(string.ascii_lowercase)

    other_chars = []
    for _ in range(length - 1):  
        if random.random() < 0.07:  
            other_chars.append(random.choice(string.digits))
        else: 
            other_chars.append(random.choice(string.ascii_lowercase))

    return first_char + ''.join(other_chars)

def OpenBrowser():
    try:
        # 配置Chrome选项
        options = uc.ChromeOptions()

        # 添加基本反检测选项
        options.add_argument('--no-first-run')
        options.add_argument('--no-default-browser-check')
        options.add_argument('--disable-blink-features=AutomationControlled')

        # 设置用户代理
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

        # 添加代理配置（如果提供了代理）
        if proxy and proxy.strip():
            options.add_argument(f'--proxy-server={proxy}')
            print(f"使用代理: {proxy}")
        else:
            print("未配置代理，使用直连")

        # 如果指定了浏览器路径，使用自定义浏览器
        if browser_path and browser_path.strip() and os.path.exists(browser_path):
            options.binary_location = browser_path
            print(f"使用自定义浏览器: {browser_path}")
        else:
            print("使用默认Chrome浏览器")

        # 创建undetected-chromedriver实例
        driver = uc.Chrome(options=options, version_main=None)

        # 设置窗口大小
        driver.set_window_size(1280, 720)

        # 执行反检测脚本
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        return driver

    except Exception as e:
        print(f"浏览器启动失败: {e}")
        return None

def Outlook_register(driver, email, password):

    fake = Faker()

    lastname = fake.last_name()
    firstname = fake.first_name()
    year = str(random.randint(1960, 2005))
    month = str(random.randint(1, 12))
    day = str(random.randint(1, 28))

    try:
        print(f"正在访问Outlook注册页面...")
        driver.get("https://outlook.live.com/mail/0/?prompt=create_account")
        print("页面加载成功，等待同意按钮...")

        # 等待同意按钮出现并点击
        wait = WebDriverWait(driver, 30)
        agree_button = wait.until(EC.element_to_be_clickable((By.XPATH, "//*[contains(text(), '同意并继续')]")))
        start_time = time.time()
        time.sleep(2)
        agree_button.click()
        print("已点击同意并继续")

    except Exception as e:
        print(f"[Error: 页面访问失败] - 无法访问Outlook注册页面: {e}")
        print("可能的原因:")
        print("1. 网络连接问题")
        print("2. 代理配置错误")
        print("3. IP被封禁")
        print("4. 页面结构发生变化")
        return False
    
    try:
        wait = WebDriverWait(driver, 10)

        # 输入邮箱地址
        email_input = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '[aria-label="新建电子邮件"]')))
        for char in email:
            email_input.send_keys(char)
            time.sleep(0.08)  # 模拟打字延迟

        # 点击下一步
        next_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-testid="primaryButton"]')))
        next_button.click()
        time.sleep(0.4)

        # 输入密码
        password_input = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '[type="password"]')))
        for char in password:
            password_input.send_keys(char)
            time.sleep(0.06)  # 模拟打字延迟

        time.sleep(0.4)
        next_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-testid="primaryButton"]')))
        next_button.click()

        time.sleep(0.5)
        # 填写出生年份
        year_input = wait.until(EC.presence_of_element_located((By.NAME, 'BirthYear')))
        year_input.clear()
        year_input.send_keys(year)

        try:
            time.sleep(0.6)
            # 尝试使用下拉选择月份
            month_select = Select(wait.until(EC.presence_of_element_located((By.NAME, 'BirthMonth'))))
            month_select.select_by_value(month)
            time.sleep(1.2)
            # 选择日期
            day_select = Select(driver.find_element(By.NAME, 'BirthDay'))
            day_select.select_by_value(day)

        except:
            # 如果下拉选择失败，尝试点击方式
            month_dropdown = driver.find_element(By.NAME, 'BirthMonth')
            month_dropdown.click()
            time.sleep(0.4)
            month_option = wait.until(EC.element_to_be_clickable((By.XPATH, f'//div[@role="option" and contains(text(), "{month}月")]')))
            month_option.click()
            time.sleep(1.2)

            day_dropdown = driver.find_element(By.NAME, 'BirthDay')
            day_dropdown.click()
            time.sleep(0.4)
            day_option = wait.until(EC.element_to_be_clickable((By.XPATH, f'//div[@role="option" and contains(text(), "{day}日")]')))
            day_option.click()

        next_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-testid="primaryButton"]')))
        next_button.click()

        # 填写姓名
        lastname_input = wait.until(EC.presence_of_element_located((By.ID, 'lastNameInput')))
        for char in lastname:
            lastname_input.send_keys(char)
            time.sleep(0.12)  # 模拟打字延迟

        time.sleep(0.7)
        firstname_input = driver.find_element(By.ID, 'firstNameInput')
        firstname_input.clear()
        firstname_input.send_keys(firstname)

        # 等待机器人保护时间
        if time.time() - start_time < bot_protection_wait:
            time.sleep(bot_protection_wait - time.time() + start_time)

        next_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-testid="primaryButton"]')))
        next_button.click()

        # 等待页面加载
        wait_long = WebDriverWait(driver, 22)
        wait_long.until(EC.invisibility_of_element_located((By.CSS_SELECTOR, 'span > [href="https://go.microsoft.com/fwlink/?LinkID=521839"]')))

        time.sleep(0.4)

        # 检查是否有异常活动提示
        try:
            abnormal_activity = driver.find_elements(By.XPATH, "//*[contains(text(), '一些异常活动')]")
            if abnormal_activity:
                print("[Error: IP or broswer] - 当前IP注册频率过快。检查IP与是否为指纹浏览器并关闭了无头模式。")
                return False
        except:
            pass

        # 检查是否有错误的验证码类型
        try:
            enforcement_frame = driver.find_elements(By.ID, 'enforcementFrame')
            if enforcement_frame:
                print("[Error: FunCaptcha] - 验证码类型错误，非按压验证码。")
                return False
        except:
            pass

        # 等待验证码加载
        try:
            wait_long = WebDriverWait(driver, 22)
            # 等待验证码iframe出现
            wait_long.until(lambda d: any("iframe.hsprotect.net" in iframe.get_attribute("src") or ""
                                        for iframe in d.find_elements(By.TAG_NAME, "iframe")
                                        if iframe.get_attribute("src")))
            time.sleep(0.8)
        except TimeoutException:
            print("[Error: Captcha] - 验证码加载超时")
            return False

        # 模拟Tab键导航到验证码
        actions = ActionChains(driver)
        actions.send_keys(Keys.TAB).perform()
        actions.send_keys(Keys.TAB).perform()
        time.sleep(0.1)

        # 尝试多次通过验证码
        for attempt in range(max_captcha_retries + 1):
            try:
                actions.send_keys(Keys.ENTER).perform()
                time.sleep(11)
                actions.send_keys(Keys.ENTER).perform()

                # 等待验证结果
                time.sleep(5)

                # 检查是否还有验证码
                try:
                    remaining_captcha = any("iframe.hsprotect.net" in iframe.get_attribute("src") or ""
                                          for iframe in driver.find_elements(By.TAG_NAME, "iframe")
                                          if iframe.get_attribute("src"))
                    if not remaining_captcha:
                        break
                except:
                    break

            except Exception as e:
                print(f"验证码尝试 {attempt + 1} 失败: {e}")
                if attempt == max_captcha_retries:
                    raise TimeoutError("验证码尝试次数已达上限")

        # 检查是否通过验证但仍有频率限制
        try:
            time.sleep(1.2)
            abnormal_activity = driver.find_elements(By.XPATH, "//*[contains(text(), '一些异常活动')]")
            if abnormal_activity:
                print("[Error: Rate limit] - 正常通过验证码，但当前IP注册频率过快。")
                return False
        except:
            pass

        time.sleep(0.5)

    except Exception as e:
        print(f"[Error: IP] - 加载超时或因触发机器人检测导致按压次数达到最大仍未通过: {e}")
        return False

    # 保存注册成功的邮箱
    filename = 'Results\\logged_email.txt' if enable_oauth2 else 'Results\\unlogged_email.txt'
    with open(filename, 'a', encoding='utf-8') as f:
        f.write(f"{email}@outlook.com: {password}\n")
    print(f'[Success: Email Registration] - {email}@outlook.com: {password}')

    if not enable_oauth2:
        return True

    # OAuth2 流程处理
    try:
        wait = WebDriverWait(driver, 20)
        secondary_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-testid="secondaryButton"]')))
        secondary_button.click()

        # 等待按钮重新出现
        wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="secondaryButton"]')))

    except Exception as e:
        print(f"[Error: Timeout] - 无法找到按钮: {e}")
        return False

    try:
        # 多次点击跳过按钮
        for i in range(3):
            try:
                time.sleep(random.randint(1600, 2000) / 1000.0)
                secondary_button = WebDriverWait(driver, 6).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-testid="secondaryButton"]'))
                )
                secondary_button.click()
            except:
                break

        time.sleep(3)
        # 最后一次点击
        try:
            secondary_button = WebDriverWait(driver, 6).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-testid="secondaryButton"]'))
            )
            secondary_button.click()
        except:
            pass

    except:
        pass

    try:
        time.sleep(3.2)
        # 检查是否有"保持登录状态"提示
        try:
            stay_signed_in = driver.find_elements(By.XPATH, "//*[contains(text(), '保持登录状态')]")
            if stay_signed_in:
                no_button = WebDriverWait(driver, 12).until(
                    EC.element_to_be_clickable((By.XPATH, "//*[contains(text(), '否')]"))
                )
                no_button.click()
        except:
            pass

        # 等待邮箱界面加载完成
        wait_long = WebDriverWait(driver, 26)
        wait_long.until(EC.presence_of_element_located((By.CSS_SELECTOR, '.splitPrimaryButton[aria-label="新邮件"]')))
        return True

    except Exception as e:
        print(f'[Error: Timeout] - 邮箱未初始化，无法正常收件: {e}')
        return False

def process_single_flow():
    driver = None
    try:
        driver = OpenBrowser()
        if not driver:
            return False

        email = random_email(random.randint(12, 14))
        password = generate_strong_password(random.randint(11, 15))
        result = Outlook_register(driver, email, password)

        if result and not enable_oauth2:
            return True

        elif not result:
            return False

        # 如果启用了OAuth2，获取token
        token_result = get_access_token(driver, email)
        if token_result[0]:
            refresh_token, access_token, expire_at = token_result
            with open(r'Results\outlook_token.txt', 'a') as f2:
                f2.write(email + "@outlook.com---" + password + "---" + refresh_token + "---" + access_token + "---" + str(expire_at) + "\n")
            print(f'[Success: TokenAuth] - {email}@outlook.com')
            return True
        else:
            return False

    except Exception as e:
        print(f"处理流程异常: {e}")
        return False

    finally:
        if driver:
            try:
                driver.quit()
            except:
                pass

def main(concurrent_flows=10, max_tasks=1000):

    task_counter = 0  
    succeeded_tasks = 0 
    failed_tasks = 0 

    with ThreadPoolExecutor(max_workers=concurrent_flows) as executor:
        running_futures = set()

        while task_counter < max_tasks or len(running_futures) > 0:

            done_futures = {f for f in running_futures if f.done()}
            for future in done_futures:
                try:
                    result = future.result()
                    if result:
                        succeeded_tasks += 1
                    else:
                        failed_tasks += 1

                except Exception as e:
                    failed_tasks += 1
                    print(e)

                running_futures.remove(future)
            
            while len(running_futures) < concurrent_flows and task_counter < max_tasks:
                time.sleep(0.2)
                new_future = executor.submit(process_single_flow)
                running_futures.add(new_future)
                task_counter += 1

            time.sleep(0.5)

        print(f"[Info: Result] - 共 {max_tasks} 个，成功 {succeeded_tasks}，失败 {failed_tasks}")

if __name__ == '__main__':

    print("=== Outlook 注册工具启动 ===")

    try:
        # Get the directory where the script is located
        script_dir = os.path.dirname(os.path.abspath(__file__))
        config_path = os.path.join(script_dir, 'config.json')

        with open(config_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print("✓ 配置文件加载成功")
    except Exception as e:
        print(f"✗ 配置文件加载失败: {e}")
        input("按回车键退出...")
        exit(1)

    os.makedirs("Results", exist_ok=True)

    browser_path = data['browser_path']
    bot_protection_wait = data['Bot_protection_wait']
    max_captcha_retries = data['max_captcha_retries']
    proxy = data['proxy']
    enable_oauth2 = data['enable_oauth2']
    concurrent_flows = data["concurrent_flows"]
    max_tasks = data["max_tasks"]

    print(f"配置信息:")
    print(f"  浏览器路径: {browser_path}")
    print(f"  代理设置: {proxy}")
    print(f"  并发数: {concurrent_flows}")
    print(f"  最大任务数: {max_tasks}")
    print(f"  启用OAuth2: {enable_oauth2}")
    print()

    # 测试浏览器启动
    print("正在测试浏览器启动...")
    try:
        driver = OpenBrowser()
        if driver:
            print("✓ 浏览器启动成功")
            driver.quit()
        else:
            print("✗ 浏览器启动失败")
            input("按回车键退出...")
            exit(1)
    except Exception as e:
        print(f"✗ 浏览器启动异常: {e}")
        input("按回车键退出...")
        exit(1)

    print("\n开始执行注册任务...")
    try:
        main(concurrent_flows, max_tasks)
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        print(f"\n程序执行异常: {e}")

    print("\n程序执行完毕")
    input("按回车键退出...")